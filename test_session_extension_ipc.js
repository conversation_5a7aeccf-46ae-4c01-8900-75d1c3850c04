/**
 * Session Extension IPC Handler Test
 * 
 * This script tests the actual IPC handlers for session extension
 * to ensure the frontend-backend communication works correctly.
 */

const { Client } = require('pg');
require('dotenv').config({ path: '.env.local' });

// Import the authentication handler functions
const { ipcMain } = require('electron');

// Mock event object for IPC testing
const mockEvent = {
  sender: {
    send: () => {},
    webContents: {
      send: () => {}
    }
  }
};

// Database connection configuration
const dbConfig = {
  host: process.env.DB_HOST,
  port: process.env.DB_PORT,
  database: process.env.DB_NAME,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  ssl: process.env.DB_SSL === 'true' ? { rejectUnauthorized: false } : false,
};

console.log('🧪 Starting Session Extension IPC Handler Test...');

async function testSessionExtensionIPC() {
  let client = null;
  
  try {
    // Connect to database to set up test data
    client = new Client(dbConfig);
    await client.connect();
    console.log('✅ Connected to database');

    // Test data
    const testUserId = 1;
    const testSessionId = 'ipc-test-session-' + Date.now();
    const testIpAddress = '********';
    const testUserAgent = 'IPC-Test-User-Agent';

    console.log('\n📝 Test Parameters:');
    console.log('   User ID:', testUserId);
    console.log('   Session ID:', testSessionId);
    console.log('   IP Address:', testIpAddress);
    console.log('   User Agent:', testUserAgent);

    // Step 1: Create a test login session
    console.log('\n🔄 Step 1: Creating test login session...');
    const loginTime = new Date();
    await client.query(`
      INSERT INTO log_history_user (user_id, user_name, action_type, ip_address, user_agent, session_id, create_dt)
      VALUES ($1, 'admin', 'LOGIN_SUCCESS', $2, $3, $4, $5)
    `, [testUserId, testIpAddress, testUserAgent, testSessionId, loginTime]);
    console.log('✅ Test login session created');

    // Step 2: Test session validation IPC handler
    console.log('\n🔄 Step 2: Testing validate-session IPC handler...');
    
    // Get the validate-session handler
    const validateHandlers = ipcMain.listeners('validate-session');
    if (validateHandlers.length === 0) {
      throw new Error('❌ validate-session handler not found');
    }
    
    const validateHandler = validateHandlers[0];
    const validateResult = await validateHandler(mockEvent, {
      user_id: testUserId,
      session_id: testSessionId
    });

    console.log('📊 Validation Result:', {
      valid: validateResult.valid,
      message: validateResult.message,
      expires_at: validateResult.expires_at?.toISOString()
    });

    if (!validateResult.valid) {
      throw new Error('❌ Session validation failed: ' + validateResult.message);
    }
    console.log('✅ Session validation successful');

    // Step 3: Test session extension IPC handler
    console.log('\n🔄 Step 3: Testing extend-session IPC handler...');
    
    // Get the extend-session handler
    const extendHandlers = ipcMain.listeners('extend-session');
    if (extendHandlers.length === 0) {
      throw new Error('❌ extend-session handler not found');
    }
    
    const extendHandler = extendHandlers[0];
    const extendResult = await extendHandler(mockEvent, {
      user_id: testUserId,
      session_id: testSessionId,
      ip_address: testIpAddress,
      user_agent: testUserAgent
    });

    console.log('📊 Extension Result:', {
      valid: extendResult.valid,
      message: extendResult.message,
      expires_at: extendResult.expires_at?.toISOString()
    });

    if (!extendResult.valid) {
      throw new Error('❌ Session extension failed: ' + extendResult.message);
    }
    console.log('✅ Session extension successful');

    // Step 4: Verify session extension was logged
    console.log('\n🔄 Step 4: Verifying session extension was logged...');
    const extensionCheck = await client.query(`
      SELECT action_type, ip_address, user_agent, create_dt AT TIME ZONE 'Asia/Bangkok' as create_dt_thai
      FROM log_history_user
      WHERE user_id = $1 AND session_id = $2 AND action_type = 'SESSION_EXTENDED'
      ORDER BY create_dt DESC
      LIMIT 1
    `, [testUserId, testSessionId]);

    if (extensionCheck.rows.length === 0) {
      throw new Error('❌ Session extension record not found in database');
    }

    const extensionRecord = extensionCheck.rows[0];
    console.log('✅ Session extension record found:');
    console.log('   Time:', extensionRecord.create_dt_thai.toISOString());
    console.log('   IP Address:', extensionRecord.ip_address);
    console.log('   User Agent:', extensionRecord.user_agent);

    // Step 5: Test validation after extension
    console.log('\n🔄 Step 5: Testing validation after extension...');
    const validateAfterExtend = await validateHandler(mockEvent, {
      user_id: testUserId,
      session_id: testSessionId
    });

    console.log('📊 Validation After Extension:', {
      valid: validateAfterExtend.valid,
      message: validateAfterExtend.message,
      expires_at: validateAfterExtend.expires_at?.toISOString()
    });

    if (!validateAfterExtend.valid) {
      throw new Error('❌ Session validation after extension failed');
    }

    // Verify the expiry time is based on extension time
    const originalExpiry = new Date(loginTime.getTime() + 60 * 60 * 1000);
    const extendedExpiry = validateAfterExtend.expires_at;
    
    console.log('📊 Expiry Time Comparison:');
    console.log('   Original expiry (login + 1h):', originalExpiry.toISOString());
    console.log('   Extended expiry:', extendedExpiry.toISOString());
    
    if (extendedExpiry > originalExpiry) {
      console.log('✅ Session expiry correctly extended beyond original time');
    } else {
      console.log('⚠️ Session expiry may not be properly extended');
    }

    // Step 6: Test edge cases
    console.log('\n🔄 Step 6: Testing edge cases...');
    
    // Test extending non-existent session
    const invalidExtendResult = await extendHandler(mockEvent, {
      user_id: testUserId,
      session_id: 'non-existent-session',
      ip_address: testIpAddress,
      user_agent: testUserAgent
    });

    if (invalidExtendResult.valid) {
      console.log('⚠️ Warning: Extension of non-existent session succeeded (unexpected)');
    } else {
      console.log('✅ Extension of non-existent session correctly failed');
    }

    // Step 7: Cleanup
    console.log('\n🔄 Step 7: Cleaning up test data...');
    const deleteResult = await client.query(`
      DELETE FROM log_history_user
      WHERE user_id = $1 AND session_id = $2
    `, [testUserId, testSessionId]);
    console.log('✅ Cleaned up', deleteResult.rowCount, 'test records');

    console.log('\n🎉 Session Extension IPC Handler Test PASSED!');
    console.log('\n📋 Test Summary:');
    console.log('   ✅ validate-session IPC handler works correctly');
    console.log('   ✅ extend-session IPC handler works correctly');
    console.log('   ✅ Session extension is properly logged to database');
    console.log('   ✅ Session validation uses extended time correctly');
    console.log('   ✅ IP address and user agent are properly handled');
    console.log('   ✅ Edge cases are handled appropriately');

  } catch (error) {
    console.error('\n❌ Session Extension IPC Handler Test FAILED!');
    console.error('Error:', error.message);
    if (error.stack) {
      console.error('Stack:', error.stack);
    }
  } finally {
    if (client) {
      await client.end();
      console.log('\n🔌 Database connection closed');
    }
  }
}

// Wait a moment for the Electron app to fully initialize
setTimeout(() => {
  testSessionExtensionIPC();
}, 3000);
