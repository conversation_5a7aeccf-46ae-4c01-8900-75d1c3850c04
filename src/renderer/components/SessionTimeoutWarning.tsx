import { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';

interface SessionTimeoutWarningProps {
  warningThresholdMinutes?: number; // Show warning when this many minutes remain
  className?: string;
}

export function SessionTimeoutWarning({
  warningThresholdMinutes = 10, // Show warning when 10 minutes remain (since we now extend for full hour)
  className = ''
}: SessionTimeoutWarningProps) {
  const { isAuthenticated, getTimeUntilExpiry, extendSession, logout, expiresAt } = useAuth();
  const [timeRemaining, setTimeRemaining] = useState<number | null>(null);
  const [showWarning, setShowWarning] = useState(false);
  const [isExtending, setIsExtending] = useState(false);

  useEffect(() => {
    if (!isAuthenticated) {
      setShowWarning(false);
      return;
    }

    const updateTimeRemaining = () => {
      const timeUntilExpiry = getTimeUntilExpiry();
      setTimeRemaining(timeUntilExpiry);

      if (timeUntilExpiry !== null) {
        const minutesRemaining = Math.floor(timeUntilExpiry / (1000 * 60));
        const shouldShowWarning = minutesRemaining <= warningThresholdMinutes && minutesRemaining > 0;
        setShowWarning(shouldShowWarning);

        if (timeUntilExpiry <= 0) {
          // Session has expired
          setShowWarning(false);
        }
      } else {
        setShowWarning(false);
      }
    };

    // Update immediately
    updateTimeRemaining();

    // Update every 30 seconds
    const interval = setInterval(updateTimeRemaining, 30 * 1000);

    return () => clearInterval(interval);
  }, [isAuthenticated, getTimeUntilExpiry, warningThresholdMinutes, expiresAt]);

  const handleExtendSession = async () => {
    setIsExtending(true);
    try {
      console.log('🔄 Attempting to extend session...');
      const success = await extendSession();
      if (success) {
        console.log('✅ Session extended successfully - hiding warning');
        setShowWarning(false);
        // Force a re-check of time remaining after a brief delay
        setTimeout(() => {
          const newTimeRemaining = getTimeUntilExpiry();
          setTimeRemaining(newTimeRemaining);
          console.log('🔄 Updated time remaining after extension:', newTimeRemaining);
        }, 500);
      } else {
        console.log('❌ Session extension failed - logging out');
        await logout();
      }
    } catch (error) {
      console.error('❌ Error extending session:', error);
      await logout();
    } finally {
      setIsExtending(false);
    }
  };

  const handleLogout = async () => {
    await logout();
  };

  const formatTimeRemaining = (milliseconds: number): string => {
    const minutes = Math.floor(milliseconds / (1000 * 60));
    const seconds = Math.floor((milliseconds % (1000 * 60)) / 1000);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  if (!showWarning || timeRemaining === null) {
    return null;
  }

  return (
    <div className={`fixed top-4 right-4 z-50 ${className}`}>
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-lg max-w-sm">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3 flex-1">
            <h3 className="text-sm font-medium text-yellow-800">
              Session Expiring Soon
            </h3>
            <div className="mt-1 text-sm text-yellow-700">
              <p>Your session will expire in {formatTimeRemaining(timeRemaining)}</p>
            </div>
            <div className="mt-3 flex space-x-2">
              <button
                type="button"
                onClick={handleExtendSession}
                disabled={isExtending}
                className="bg-yellow-100 text-yellow-800 hover:bg-yellow-200 focus:ring-2 focus:ring-yellow-600 focus:ring-offset-2 focus:ring-offset-yellow-50 text-xs font-medium px-3 py-1.5 rounded-md disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isExtending ? 'Extending...' : 'Extend Session'}
              </button>
              <button
                type="button"
                onClick={handleLogout}
                className="bg-white text-yellow-800 hover:bg-gray-50 focus:ring-2 focus:ring-yellow-600 focus:ring-offset-2 focus:ring-offset-yellow-50 text-xs font-medium px-3 py-1.5 rounded-md border border-yellow-300"
              >
                Logout Now
              </button>
            </div>
          </div>
          <div className="ml-4 flex-shrink-0">
            <button
              type="button"
              onClick={() => setShowWarning(false)}
              className="bg-yellow-50 rounded-md inline-flex text-yellow-400 hover:text-yellow-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-yellow-50 focus:ring-yellow-600"
            >
              <span className="sr-only">Dismiss</span>
              <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

// Hook for getting session time remaining WITHOUT auto-logout
// Auto-logout is handled by AuthContext to prevent race conditions
export function useSessionTimeRemaining() {
  const { isAuthenticated, getTimeUntilExpiry } = useAuth();
  const [timeRemaining, setTimeRemaining] = useState<number | null>(null);

  useEffect(() => {
    if (!isAuthenticated) {
      setTimeRemaining(null);
      return;
    }

    const updateTime = () => {
      const currentTimeRemaining = getTimeUntilExpiry();
      setTimeRemaining(currentTimeRemaining);

      // Note: Auto-logout is handled by AuthContext session monitoring
      // to prevent race conditions when extending sessions
    };

    updateTime();
    const interval = setInterval(updateTime, 1000); // Update every second

    return () => clearInterval(interval);
  }, [isAuthenticated, getTimeUntilExpiry]);

  return timeRemaining;
}

// Hook for session timeout warning
export function useSessionTimeoutWarning(warningThresholdMinutes: number = 10) {
  const timeRemaining = useSessionTimeRemaining();
  const [shouldShowWarning, setShouldShowWarning] = useState(false);

  useEffect(() => {
    if (timeRemaining !== null) {
      const minutesRemaining = Math.floor(timeRemaining / (1000 * 60));
      setShouldShowWarning(minutesRemaining <= warningThresholdMinutes && minutesRemaining > 0);
    } else {
      setShouldShowWarning(false);
    }
  }, [timeRemaining, warningThresholdMinutes]);

  return {
    shouldShowWarning,
    timeRemaining,
    minutesRemaining: timeRemaining ? Math.floor(timeRemaining / (1000 * 60)) : null,
    secondsRemaining: timeRemaining ? Math.floor((timeRemaining % (1000 * 60)) / 1000) : null
  };
}
