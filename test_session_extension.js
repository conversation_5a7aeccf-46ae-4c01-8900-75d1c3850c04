/**
 * Session Extension Workflow Test
 * 
 * This script tests the complete session extension workflow to ensure:
 * 1. Session extension creates proper database records
 * 2. Session validation uses the extended time correctly
 * 3. IP address and user agent are properly logged
 * 4. Session expiry is calculated from the extension time
 */

const { Client } = require('pg');
require('dotenv').config({ path: '.env.local' });

// Database connection configuration
const dbConfig = {
  host: process.env.DB_HOST,
  port: process.env.DB_PORT,
  database: process.env.DB_NAME,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  ssl: process.env.DB_SSL === 'true' ? { rejectUnauthorized: false } : false,
};

console.log('🧪 Starting Session Extension Workflow Test...');
console.log('📊 Database config:', {
  host: dbConfig.host,
  port: dbConfig.port,
  database: dbConfig.database,
  user: dbConfig.user,
  ssl: !!dbConfig.ssl
});

async function testSessionExtensionWorkflow() {
  let client = null;
  
  try {
    // Connect to database
    client = new Client(dbConfig);
    await client.connect();
    console.log('✅ Connected to database');

    // Test data
    const testUserId = 1; // Assuming admin user exists
    const testSessionId = 'test-session-' + Date.now();
    const testIpAddress = '*************';
    const testUserAgent = 'Test-Session-Extension-Script';

    console.log('\n📝 Test Parameters:');
    console.log('   User ID:', testUserId);
    console.log('   Session ID:', testSessionId);
    console.log('   IP Address:', testIpAddress);
    console.log('   User Agent:', testUserAgent);

    // Step 1: Create a test login session
    console.log('\n🔄 Step 1: Creating test login session...');
    const loginTime = new Date();
    await client.query(`
      INSERT INTO log_history_user (user_id, user_name, action_type, ip_address, user_agent, session_id, create_dt)
      VALUES ($1, 'admin', 'LOGIN_SUCCESS', $2, $3, $4, $5)
    `, [testUserId, testIpAddress, testUserAgent, testSessionId, loginTime]);
    console.log('✅ Test login session created at:', loginTime.toISOString());

    // Step 2: Verify session validation works (before extension)
    console.log('\n🔄 Step 2: Testing session validation before extension...');
    const sessionCheck1 = await client.query(`
      SELECT *, create_dt AT TIME ZONE 'Asia/Bangkok' as create_dt_thai
      FROM log_history_user
      WHERE user_id = $1 AND session_id = $2 AND action_type = 'LOGIN_SUCCESS'
      ORDER BY create_dt DESC
      LIMIT 1
    `, [testUserId, testSessionId]);

    if (sessionCheck1.rows.length > 0) {
      const session = sessionCheck1.rows[0];
      const sessionAge = (Date.now() - session.create_dt_thai.getTime()) / 1000 / 60; // minutes
      console.log('✅ Original session found, age:', sessionAge.toFixed(2), 'minutes');
      console.log('   Session time:', session.create_dt_thai.toISOString());
    } else {
      throw new Error('❌ Test login session not found');
    }

    // Step 3: Create session extension (simulate the extend-session handler)
    console.log('\n🔄 Step 3: Creating session extension...');
    const extensionTime = new Date();
    await client.query(`
      INSERT INTO log_history_user (user_id, user_name, action_type, ip_address, user_agent, session_id, create_dt)
      VALUES ($1, 'admin', 'SESSION_EXTENDED', $2, $3, $4, $5)
    `, [testUserId, testIpAddress, testUserAgent, testSessionId, extensionTime]);
    console.log('✅ Session extension created at:', extensionTime.toISOString());

    // Step 4: Test session validation logic (simulate the validate-session handler)
    console.log('\n🔄 Step 4: Testing session validation after extension...');
    
    // Check for session extensions first (this is the new logic)
    const extensionResult = await client.query(`
      SELECT *, create_dt AT TIME ZONE 'Asia/Bangkok' as create_dt_thai
      FROM log_history_user
      WHERE user_id = $1 AND session_id = $2 AND action_type = 'SESSION_EXTENDED'
      ORDER BY create_dt DESC
      LIMIT 1
    `, [testUserId, testSessionId]);

    let effectiveSessionTime;
    let sessionType;

    if (extensionResult.rows.length > 0) {
      effectiveSessionTime = extensionResult.rows[0].create_dt_thai;
      sessionType = 'SESSION_EXTENDED';
      console.log('✅ Found session extension, using extension time:', effectiveSessionTime.toISOString());
    } else {
      // Fall back to original login time
      effectiveSessionTime = sessionCheck1.rows[0].create_dt_thai;
      sessionType = 'LOGIN_SUCCESS';
      console.log('✅ No extension found, using original login time:', effectiveSessionTime.toISOString());
    }

    // Step 5: Calculate expiry and validate session
    console.log('\n🔄 Step 5: Validating session expiry logic...');
    const currentTime = new Date();
    const sessionAgeMs = currentTime.getTime() - effectiveSessionTime.getTime();
    const sessionAgeMinutes = sessionAgeMs / 1000 / 60;
    const oneHourMs = 60 * 60 * 1000;
    const isExpired = sessionAgeMs > oneHourMs;
    const expiresAt = new Date(effectiveSessionTime.getTime() + oneHourMs);
    const timeUntilExpiry = expiresAt.getTime() - currentTime.getTime();

    console.log('📊 Session Validation Results:');
    console.log('   Effective session time:', effectiveSessionTime.toISOString());
    console.log('   Session type:', sessionType);
    console.log('   Current time:', currentTime.toISOString());
    console.log('   Session age:', sessionAgeMinutes.toFixed(2), 'minutes');
    console.log('   Expires at:', expiresAt.toISOString());
    console.log('   Time until expiry:', (timeUntilExpiry / 1000 / 60).toFixed(2), 'minutes');
    console.log('   Is expired:', isExpired);

    if (!isExpired) {
      console.log('✅ Session is valid and not expired');
    } else {
      console.log('❌ Session is expired');
    }

    // Step 6: Verify database records
    console.log('\n🔄 Step 6: Verifying database records...');
    const allRecords = await client.query(`
      SELECT action_type, ip_address, user_agent, create_dt AT TIME ZONE 'Asia/Bangkok' as create_dt_thai
      FROM log_history_user
      WHERE user_id = $1 AND session_id = $2
      ORDER BY create_dt ASC
    `, [testUserId, testSessionId]);

    console.log('📊 Session History Records:');
    allRecords.rows.forEach((record, index) => {
      console.log(`   ${index + 1}. ${record.action_type} at ${record.create_dt_thai.toISOString()}`);
      console.log(`      IP: ${record.ip_address}, User Agent: ${record.user_agent}`);
    });

    // Step 7: Test multiple extensions
    console.log('\n🔄 Step 7: Testing multiple session extensions...');
    const secondExtensionTime = new Date(Date.now() + 5000); // 5 seconds later
    await client.query(`
      INSERT INTO log_history_user (user_id, user_name, action_type, ip_address, user_agent, session_id, create_dt)
      VALUES ($1, 'admin', 'SESSION_EXTENDED', $2, $3, $4, $5)
    `, [testUserId, testIpAddress, 'Second-Extension-Test', testSessionId, secondExtensionTime]);

    // Check that the most recent extension is used
    const latestExtension = await client.query(`
      SELECT *, create_dt AT TIME ZONE 'Asia/Bangkok' as create_dt_thai
      FROM log_history_user
      WHERE user_id = $1 AND session_id = $2 AND action_type = 'SESSION_EXTENDED'
      ORDER BY create_dt DESC
      LIMIT 1
    `, [testUserId, testSessionId]);

    if (latestExtension.rows.length > 0) {
      const latest = latestExtension.rows[0];
      console.log('✅ Latest extension found:', latest.create_dt_thai.toISOString());
      console.log('   User Agent:', latest.user_agent);
    }

    // Step 8: Cleanup test data
    console.log('\n🔄 Step 8: Cleaning up test data...');
    const deleteResult = await client.query(`
      DELETE FROM log_history_user
      WHERE user_id = $1 AND session_id = $2
    `, [testUserId, testSessionId]);
    console.log('✅ Cleaned up', deleteResult.rowCount, 'test records');

    console.log('\n🎉 Session Extension Workflow Test PASSED!');
    console.log('\n📋 Test Summary:');
    console.log('   ✅ Session extension records are created correctly');
    console.log('   ✅ Session validation uses the most recent extension time');
    console.log('   ✅ IP address and user agent are properly logged');
    console.log('   ✅ Session expiry is calculated from extension time');
    console.log('   ✅ Multiple extensions work correctly');
    console.log('   ✅ Database cleanup successful');

  } catch (error) {
    console.error('\n❌ Session Extension Workflow Test FAILED!');
    console.error('Error:', error.message);
    console.error('Stack:', error.stack);
  } finally {
    if (client) {
      await client.end();
      console.log('\n🔌 Database connection closed');
    }
  }
}

// Run the test
testSessionExtensionWorkflow();
